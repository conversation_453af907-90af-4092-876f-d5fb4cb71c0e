/* 现代化弹窗样式 */
.modern-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-modal-overlay.show {
    opacity: 1;
}

.modern-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.7) translateY(-50px);
    transition: transform 0.3s ease;
}

.modern-modal-overlay.show .modern-modal {
    transform: scale(1) translateY(0);
}

.modern-modal-header {
    padding: 20px 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none;
}

.modern-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.modern-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modern-modal-close:hover {
    background: #f5f5f5;
    color: #666;
}

.modern-modal-body {
    padding: 20px 24px;
    text-align: center;
}

.modern-modal-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.modern-modal-icon.success {
    color: #52c41a;
}

.modern-modal-icon.error {
    color: #ff4d4f;
}

.modern-modal-icon.warning {
    color: #faad14;
}

.modern-modal-icon.info {
    color: #1890ff;
}

.modern-modal-icon.loading {
    color: #1890ff;
}

.modern-modal-content {
    font-size: 16px;
    line-height: 1.5;
    color: #666;
    margin-bottom: 8px;
}

.modern-modal-footer {
    padding: 0 24px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.modern-modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modern-modal-btn-primary {
    background: #1890ff;
    color: white;
}

.modern-modal-btn-primary:hover {
    background: #40a9ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.modern-modal-btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #d9d9d9;
}

.modern-modal-btn-secondary:hover {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modern-modal {
        width: 95%;
        margin: 20px;
    }
    
    .modern-modal-header,
    .modern-modal-body,
    .modern-modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .modern-modal-footer {
        flex-direction: column;
    }
    
    .modern-modal-btn {
        width: 100%;
        margin-bottom: 8px;
    }
}

/* 动画效果 */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.7) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.7) translateY(-50px);
    }
}

/* 特殊样式 */
.modern-modal-content .tx-hash {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    word-break: break-all;
    margin-top: 12px;
}

.modern-modal-content .amount {
    font-weight: 600;
    color: #1890ff;
    font-size: 18px;
}

.modern-modal-content .address {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    word-break: break-all;
    display: inline-block;
    margin: 4px 0;
}
