/**
 * 现代化弹窗组件
 */
class ModernModal {
    constructor() {
        this.createModalContainer();
    }

    createModalContainer() {
        // 如果已存在则不重复创建
        if (document.getElementById('modern-modal-container')) {
            return;
        }

        const modalHTML = `
            <div id="modern-modal-container" class="modern-modal-overlay" style="display: none;">
                <div class="modern-modal">
                    <div class="modern-modal-header">
                        <h4 class="modern-modal-title"></h4>
                        <button class="modern-modal-close">&times;</button>
                    </div>
                    <div class="modern-modal-body">
                        <div class="modern-modal-icon"></div>
                        <div class="modern-modal-content"></div>
                    </div>
                    <div class="modern-modal-footer">
                        <button class="modern-modal-btn modern-modal-btn-secondary" id="modal-cancel">取消</button>
                        <button class="modern-modal-btn modern-modal-btn-primary" id="modal-confirm">确定</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.bindEvents();
    }

    bindEvents() {
        const container = document.getElementById('modern-modal-container');
        const closeBtn = container.querySelector('.modern-modal-close');
        const cancelBtn = container.querySelector('#modal-cancel');

        // 点击关闭按钮
        closeBtn.addEventListener('click', () => this.hide());
        
        // 点击取消按钮
        cancelBtn.addEventListener('click', () => this.hide());

        // 点击遮罩层关闭
        container.addEventListener('click', (e) => {
            if (e.target === container) {
                this.hide();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && container.style.display !== 'none') {
                this.hide();
            }
        });
    }

    show(options = {}) {
        const container = document.getElementById('modern-modal-container');
        const title = container.querySelector('.modern-modal-title');
        const icon = container.querySelector('.modern-modal-icon');
        const content = container.querySelector('.modern-modal-content');
        const footer = container.querySelector('.modern-modal-footer');
        const confirmBtn = container.querySelector('#modal-confirm');
        const cancelBtn = container.querySelector('#modal-cancel');

        // 设置标题
        title.textContent = options.title || '提示';

        // 设置图标
        icon.innerHTML = this.getIcon(options.type || 'info');
        icon.className = `modern-modal-icon ${options.type || 'info'}`;

        // 设置内容
        content.innerHTML = options.content || '';

        // 设置按钮
        if (options.showCancel === false) {
            cancelBtn.style.display = 'none';
        } else {
            cancelBtn.style.display = 'inline-block';
            cancelBtn.textContent = options.cancelText || '取消';
        }

        confirmBtn.textContent = options.confirmText || '确定';

        // 绑定确认按钮事件
        confirmBtn.onclick = () => {
            if (options.onConfirm) {
                options.onConfirm();
            }
            this.hide();
        };

        // 绑定取消按钮事件
        cancelBtn.onclick = () => {
            if (options.onCancel) {
                options.onCancel();
            }
            this.hide();
        };

        // 显示弹窗
        container.style.display = 'flex';
        setTimeout(() => {
            container.classList.add('show');
        }, 10);
    }

    hide() {
        const container = document.getElementById('modern-modal-container');
        container.classList.remove('show');
        setTimeout(() => {
            container.style.display = 'none';
        }, 300);
    }

    getIcon(type) {
        const icons = {
            success: '<i class="fas fa-check-circle"></i>',
            error: '<i class="fas fa-times-circle"></i>',
            warning: '<i class="fas fa-exclamation-triangle"></i>',
            info: '<i class="fas fa-info-circle"></i>',
            loading: '<i class="fas fa-spinner fa-spin"></i>'
        };
        return icons[type] || icons.info;
    }

    // 静态方法，方便调用
    static alert(content, title = '提示', type = 'info') {
        const modal = new ModernModal();
        return new Promise((resolve) => {
            modal.show({
                title,
                content,
                type,
                showCancel: false,
                onConfirm: resolve
            });
        });
    }

    static confirm(content, title = '确认', type = 'warning') {
        const modal = new ModernModal();
        return new Promise((resolve) => {
            modal.show({
                title,
                content,
                type,
                onConfirm: () => resolve(true),
                onCancel: () => resolve(false)
            });
        });
    }

    static success(content, title = '成功') {
        return ModernModal.alert(content, title, 'success');
    }

    static error(content, title = '错误') {
        return ModernModal.alert(content, title, 'error');
    }

    static warning(content, title = '警告') {
        return ModernModal.alert(content, title, 'warning');
    }

    static loading(content = '处理中...', title = '请稍候') {
        const modal = new ModernModal();
        modal.show({
            title,
            content,
            type: 'loading',
            showCancel: false
        });
        return modal;
    }
}

// 全局可用
window.ModernModal = ModernModal;
